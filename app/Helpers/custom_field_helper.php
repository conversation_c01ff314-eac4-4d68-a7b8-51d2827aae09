<?php

/**
 * Custom Field Helper Functions
 * 
 * Helper functions for Custom Fields auto title generation (Phương án 1: Auto-generate hoàn toàn)
 */

if (!function_exists('generateTitleFromCustomFields')) {
    /**
     * Generate product title from ALL custom fields based on field order
     * Auto-generate title completely from all available custom fields
     * 
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @param string $separator Separator between field values (default: ' ')
     * @return string Generated title
     */
    function generateTitleFromCustomFields($productId, $categoryId, $separator = ' ')
    {
        $fieldModel = new \App\Models\FieldModel();
        
        // Get ALL custom fields for this category (ordered by field_order)
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];
        
        foreach ($customFields as $field) {
            $fieldValue = getCustomFieldValueForTitle($field, $productId);
            
            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        // Join ALL title parts with separator
        $generatedTitle = implode($separator, $titleParts);
        
        // Clean and format title
        $generatedTitle = cleanTitleString($generatedTitle);
        
        return $generatedTitle;
    }
}

if (!function_exists('getCustomFieldValueForTitle')) {
    /**
     * Get custom field value formatted for title generation
     * 
     * @param object $field Custom field object
     * @param int $productId Product ID
     * @return string Formatted field value
     */
    function getCustomFieldValueForTitle($field, $productId)
    {
        $fieldModel = new \App\Models\FieldModel();
        
        switch ($field->field_type) {
            case 'text':
            case 'number':
            case 'textarea':
            case 'date':
                // Get direct input value
                $value = $fieldModel->getProductCustomFieldInputValue($field->id, $productId);
                return trim($value);
                
            case 'dropdown':
            case 'radio_button':
                // Get selected option name
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $firstValue = $fieldValues[0];
                    if (!empty($firstValue->selected_option_id)) {
                        return getCustomFieldOptionName($firstValue->name_data, selectedLangId(), false);
                    }
                }
                break;
                
            case 'checkbox':
                // Get ALL selected options for checkbox (join with comma)
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $optionNames = [];
                    foreach ($fieldValues as $value) {
                        if (!empty($value->selected_option_id)) {
                            $optionName = getCustomFieldOptionName($value->name_data, selectedLangId(), false);
                            if (!empty($optionName)) {
                                $optionNames[] = $optionName;
                            }
                        }
                    }
                    return implode(', ', $optionNames);
                }
                break;
        }
        
        return '';
    }
}

if (!function_exists('cleanTitleString')) {
    /**
     * Clean and format title string
     * 
     * @param string $title Raw title string
     * @return string Cleaned title
     */
    function cleanTitleString($title)
    {
        // Remove extra spaces
        $title = preg_replace('/\s+/', ' ', $title);
        
        // Trim whitespace
        $title = trim($title);
        
        // Limit title length to reasonable size
        $maxLength = 150; // Reasonable length for product titles
        if (strlen($title) > $maxLength) {
            $title = substr($title, 0, $maxLength);
            // Try to cut at word boundary
            $lastSpace = strrpos($title, ' ');
            if ($lastSpace !== false && $lastSpace > $maxLength * 0.8) {
                $title = substr($title, 0, $lastSpace);
            }
            $title = rtrim($title, '.,;:-') . '...';
        }
        
        return $title;
    }
}

if (!function_exists('autoGenerateTitleOnSave')) {
    /**
     * Automatically generate and update title when product is saved
     * This is the main function called during product save operations
     * 
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @return bool Success status
     */
    function autoGenerateTitleOnSave($productId, $categoryId)
    {
        // Generate title from custom fields
        $generatedTitle = generateTitleFromCustomFields($productId, $categoryId);
        
        if (empty($generatedTitle)) {
            return false;
        }
        
        // Update title in database for all languages
        $productModel = new \App\Models\ProductModel();
        return $productModel->updateAutoGeneratedTitle($productId, $generatedTitle);
    }
}

if (!function_exists('previewTitleFromCustomFields')) {
    /**
     * Preview title generation without saving (for AJAX calls)
     * 
     * @param array $customFieldValues Array of field_id => value
     * @param int $categoryId Category ID
     * @return string Preview title
     */
    function previewTitleFromCustomFields($customFieldValues, $categoryId)
    {
        $fieldModel = new \App\Models\FieldModel();
        
        // Get ALL custom fields for this category
        $customFields = $fieldModel->getCustomFieldsByCategory($categoryId);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];
        
        foreach ($customFields as $field) {
            $fieldValue = '';
            
            // Get value from provided array
            if (isset($customFieldValues[$field->id])) {
                $value = $customFieldValues[$field->id];
                
                switch ($field->field_type) {
                    case 'text':
                    case 'number':
                    case 'textarea':
                    case 'date':
                        $fieldValue = trim($value);
                        break;
                        
                    case 'dropdown':
                    case 'radio_button':
                        // Get option name by ID
                        if (!empty($value)) {
                            $option = $fieldModel->getFieldOption($value);
                            if (!empty($option)) {
                                $fieldValue = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                            }
                        }
                        break;
                        
                    case 'checkbox':
                        // Handle multiple values for checkbox
                        if (is_array($value) && !empty($value)) {
                            $optionNames = [];
                            foreach ($value as $optionId) {
                                $option = $fieldModel->getFieldOption($optionId);
                                if (!empty($option)) {
                                    $optionNames[] = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                                }
                            }
                            $fieldValue = implode(', ', $optionNames);
                        }
                        break;
                }
            }
            
            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        $generatedTitle = implode(' ', $titleParts);
        return cleanTitleString($generatedTitle);
    }
}
