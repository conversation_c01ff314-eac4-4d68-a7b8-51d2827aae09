<?php

/**
 * Custom Field Helper Functions
 * 
 * Helper functions for Custom Fields auto title generation and related operations
 */

if (!function_exists('generateTitleFromCustomFields')) {
    /**
     * Generate product title from custom fields based on field order
     * 
     * @param int $productId Product ID
     * @param int $categoryId Category ID
     * @param int $maxFields Maximum number of fields to use for title (default: 4)
     * @param string $separator Separator between field values (default: ' ')
     * @return string Generated title
     */
    function generateTitleFromCustomFields($productId, $categoryId, $maxFields = 4, $separator = ' ')
    {
        $fieldModel = new \App\Models\FieldModel();
        
        // Get custom fields for title generation (ordered by field_order)
        $customFields = $fieldModel->getCustomFieldsForTitleGeneration($categoryId, $maxFields);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];
        
        foreach ($customFields as $field) {
            $fieldValue = getCustomFieldValueForTitle($field, $productId);
            
            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        // Join title parts with separator
        $generatedTitle = implode($separator, $titleParts);
        
        // Clean and format title
        $generatedTitle = cleanTitleString($generatedTitle);
        
        return $generatedTitle;
    }
}

if (!function_exists('getCustomFieldValueForTitle')) {
    /**
     * Get custom field value formatted for title generation
     * 
     * @param object $field Custom field object
     * @param int $productId Product ID
     * @return string Formatted field value
     */
    function getCustomFieldValueForTitle($field, $productId)
    {
        $fieldModel = new \App\Models\FieldModel();
        
        switch ($field->field_type) {
            case 'text':
            case 'number':
            case 'textarea':
            case 'date':
                // Get direct input value
                $value = $fieldModel->getProductCustomFieldInputValue($field->id, $productId);
                return trim($value);
                
            case 'dropdown':
            case 'radio_button':
                // Get selected option name
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $firstValue = $fieldValues[0];
                    if (!empty($firstValue->selected_option_id)) {
                        return getCustomFieldOptionName($firstValue->name_data, selectedLangId(), false);
                    }
                }
                break;
                
            case 'checkbox':
                // Get first selected option for checkbox (to keep title short)
                $fieldValues = $fieldModel->getProductCustomFieldValues($field->id, $productId);
                if (!empty($fieldValues)) {
                    $firstValue = $fieldValues[0];
                    if (!empty($firstValue->selected_option_id)) {
                        return getCustomFieldOptionName($firstValue->name_data, selectedLangId(), false);
                    }
                }
                break;
        }
        
        return '';
    }
}

if (!function_exists('cleanTitleString')) {
    /**
     * Clean and format title string
     * 
     * @param string $title Raw title string
     * @return string Cleaned title
     */
    function cleanTitleString($title)
    {
        // Remove extra spaces
        $title = preg_replace('/\s+/', ' ', $title);
        
        // Trim whitespace
        $title = trim($title);
        
        // Limit title length (optional)
        $maxLength = 200; // Adjust as needed
        if (strlen($title) > $maxLength) {
            $title = substr($title, 0, $maxLength);
            // Try to cut at word boundary
            $lastSpace = strrpos($title, ' ');
            if ($lastSpace !== false && $lastSpace > $maxLength * 0.8) {
                $title = substr($title, 0, $lastSpace);
            }
            $title = rtrim($title, '.,;:-') . '...';
        }
        
        return $title;
    }
}

if (!function_exists('shouldAutoGenerateTitle')) {
    /**
     * Check if auto title generation is enabled for a category
     * 
     * @param int $categoryId Category ID
     * @return bool True if auto generation is enabled
     */
    function shouldAutoGenerateTitle($categoryId)
    {
        // Get category settings
        $category = getCategory($categoryId);
        
        if (empty($category)) {
            return false;
        }
        
        // Check if category has auto_title_enabled column and it's enabled
        if (isset($category->auto_title_enabled)) {
            return $category->auto_title_enabled == 1;
        }
        
        // Default to enabled if column doesn't exist yet
        return true;
    }
}

if (!function_exists('getTitleTemplate')) {
    /**
     * Get title template for a category (for future template-based generation)
     * 
     * @param int $categoryId Category ID
     * @return string|null Title template or null if not set
     */
    function getTitleTemplate($categoryId)
    {
        $category = getCategory($categoryId);
        
        if (empty($category)) {
            return null;
        }
        
        // Check if category has title_template column
        if (isset($category->title_template) && !empty($category->title_template)) {
            return $category->title_template;
        }
        
        return null;
    }
}

if (!function_exists('previewTitleFromCustomFields')) {
    /**
     * Preview title generation without saving (for AJAX calls)
     * 
     * @param array $customFieldValues Array of field_id => value
     * @param int $categoryId Category ID
     * @param int $maxFields Maximum fields to use
     * @return string Preview title
     */
    function previewTitleFromCustomFields($customFieldValues, $categoryId, $maxFields = 4)
    {
        $fieldModel = new \App\Models\FieldModel();
        
        // Get custom fields for title generation
        $customFields = $fieldModel->getCustomFieldsForTitleGeneration($categoryId, $maxFields);
        
        if (empty($customFields)) {
            return '';
        }
        
        $titleParts = [];
        
        foreach ($customFields as $field) {
            $fieldValue = '';
            
            // Get value from provided array
            if (isset($customFieldValues[$field->id])) {
                $value = $customFieldValues[$field->id];
                
                switch ($field->field_type) {
                    case 'text':
                    case 'number':
                    case 'textarea':
                    case 'date':
                        $fieldValue = trim($value);
                        break;
                        
                    case 'dropdown':
                    case 'radio_button':
                        // Get option name by ID
                        if (!empty($value)) {
                            $option = $fieldModel->getFieldOption($value);
                            if (!empty($option)) {
                                $fieldValue = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                            }
                        }
                        break;
                        
                    case 'checkbox':
                        // Handle multiple values for checkbox
                        if (is_array($value) && !empty($value)) {
                            $option = $fieldModel->getFieldOption($value[0]);
                            if (!empty($option)) {
                                $fieldValue = getCustomFieldOptionName($option->name_data, selectedLangId(), false);
                            }
                        }
                        break;
                }
            }
            
            if (!empty($fieldValue)) {
                $titleParts[] = $fieldValue;
            }
        }
        
        $generatedTitle = implode(' ', $titleParts);
        return cleanTitleString($generatedTitle);
    }
}
